"use client";
import React, { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import UpdateEmployee from "../manage_employee/UpdateEmployee";
import DeleteRow from "@/app/_component/DeleteRow";
import { client_routes } from "@/lib/routePath";

import { PermissionWrapper } from "@/lib/permissionWrapper";
import { useRouter, useSearchParams } from "next/navigation";
import { Branch } from "../manage_branch/Column";
import { Employee } from "../manage_employee/column";
import { ColDef } from "ag-grid-community";
import { min } from "date-fns";
import PinnedHeader from "@/app/_component/PinnedHeader";

export interface Client {
  branch: any;
  branch_id: any;
  user: any;
  id: any;
  client_name: string;
  ownership: string;
  country: string;
  client_id: any;
  corporation_id: any;
  created_at: any;
  updated_at: any;
  associateId: any;
  associate: any;
  // permissions: string[];
}

// Define this component above or outside your column definition
const TrackSheetWithPopup = ({
  client,
  permissions,
  userData,
  clientId,
}: any) => {
  console.log("Client ID in TrackSheetWithPopup:", clientId);
  const [isOpen, setIsOpen] = useState(false);
  const [showAddCustomField, setShowAddCustomField] = useState(false);
  //console.log(userData)

  function openAddCustomField() {
  setShowAddCustomField(true);
}

  return (
    <div className="flex items-center">
      <PermissionWrapper
        permissions={permissions}
        requiredPermissions={[
          "view-setup",
          "update-setup",
          "create-setup",
          "delete-setup",
          "view-client",
          "update-client",
          "delete-client",
          "create-client",
        ]}
      >
      </PermissionWrapper>
    </div>
  );
};

export interface Carrier {
  name: string;
  register1: string;
  code: string;
  country: string;
  state: string;
  city: string;
  address: string;
  phone: string;
  postalcode: string;
  carrier_id: any;
}

export const column = (
  permissions: string[],
  allBranch: any[],
  allUser: any[],
  allAssociate: any[],
  userData: any[],
  router: ReturnType<typeof useRouter>
) => {
  const columns = [
    {
      field: "name",
      headerName: "Custom Field Name",
      filter: "agTextColumnFilter",
     filterParams: {
        buttons: ["apply", "reset"],
      },
      headerComponent: PinnedHeader,
    },
    {
      field: "type",
      headerName: "Field Type",
      valueGetter: (params: any) => {
        const field = params.data;
        if (field.type === "AUTO" && field.autoOption) {
          return `Auto - ${field.autoOption.charAt(0).toUpperCase() + field.autoOption.slice(1).toLowerCase()}`;
        }
        return field.type?.charAt(0).toUpperCase() + field.type?.slice(1).toLowerCase() || "Text";
      },
      filter: "agTextColumnFilter",
     filterParams: {
        buttons: ["apply", "reset"],
      },
      headerComponent: PinnedHeader,
    },

    {
      field: "clients",
      headerName: "Client List",
      cellRenderer: (params: any) => {
        const field = params?.data;
        const clients = field?.clients || [];

        if (clients.length === 0) {
          return (
            <div className="text-gray-500 italic">
              No clients using this field
            </div>
          );
        }

        // Display all clients as comma-separated values
        const clientNames = clients.map((client: any) => client.client_name);
        const clientsText = clientNames.join(", ");

        return (
          <div className="text-sm" title={clientsText}>
            {clientsText}
          </div>
        );
      },
      // Add valueGetter for search functionality
      valueGetter: (params: any) => {
        const field = params?.data;
        const clients = field?.clients || [];
        return clients.map((client: any) => client.client_name).join(", ");
      },
      sortable: false,
      filter:false,
      //width: 350,
      //minWidth: 250,
    },

    // {
    //   field: "action",
    //   headerName: "Action",
    //   cellRenderer: (params: any) => {
    //     const client = params?.data;
    //     return (
    //       <div className="flex items-center">
    //         <PermissionWrapper
    //           permissions={permissions}
    //           requiredPermissions={["update-client"]}
    //         >
    //           <UpdateClient
    //             data={client}
    //             allBranch={allBranch}
    //             allUser={allUser}
    //             allAssociate={allAssociate}
    //           />
    //         </PermissionWrapper>

    //         <PermissionWrapper
    //           permissions={permissions}
    //           requiredPermissions={["delete-client"]}
    //         >
    //           <DeleteRow
    //             route={`${client_routes.DELETE_CLIENT}/${client?.id}`}
    //           />
    //         </PermissionWrapper>
    //       </div>
    //     );
    //   },
    //   sortable: false,
    //   width: 130,
    //   minWidth: 100,
    //   cellStyle: {
    //     display: "flex",
    //     justifyContent: "center",
    //     alignItems: "center",
    //     textAlign: "center", // Fallback
    //   },
    // },
  ];
  return columns;
};