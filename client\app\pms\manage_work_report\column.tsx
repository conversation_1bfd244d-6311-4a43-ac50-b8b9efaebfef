"use client";
import React, { createContext, useContext, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import DeleteRow from "@/app/_component/DeleteRow";
import { corporation_routes, workreport_routes } from "@/lib/routePath";
import { WorkReportContext } from "./WorkReportContext";
import { formatDate, formatDuration, formatTimeZone } from "@/lib/swrFetching";
import { ArrowUpDown } from "lucide-react";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { Header } from "@radix-ui/react-accordion";
import UpdateWorkReport from "./UpdateWorkReport";
import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
import { useSearchParams } from "next/navigation";
import { DateTime } from "luxon";
import PinnedHeader from "@/app/_component/PinnedHeader";

// The same WorkReport interface as before
interface DataTableProps {
  rowData: any[]; // Data for the rows
  permissions: any[]; // Permissions to be passed to components like Update or Delete
  setDeletedData: Function; // A function to handle data updates
  deleteData: boolean; // A boolean indicating if data is deleted
  totalPages: number; // Total number of pages for pagination
  columns: any[]; // The columns for the table (this needs to be defined)
}

export interface WorkReport {
  work_report_id: number;
  date: string;
  id: number;
  user: User;
  client_id: number;
  client: Client;
  carrier_id?: number | null;
  carrier?: Carrier | null;
  work_type_id: number;
  work_type: WorkType;
  category: any;
  planning_nummbers?: string | null;
  expected_time: string;
  actual_number?: string | null;
  start_time: string;
  finish_time: string;
  time_spent: string;
  notes: string;
  created_at: string;
  updated_at: string;
  permissions: any[];
  task_type: any;
}

interface User {
  id: number;
  corporation_id?: number | null;
  user_type: "HR" | "TL" | "CSA" | "NORMAL_MEMBER"; // Enum for user types
  role_id: number;
  email: string;
  username: string;
  password: string;
  created_at: string;
  updated_at: string;
  WorkReport: WorkReport[]; // Assuming you have a WorkReport interface defined
}

interface Client {
  client_id: number;
  corporation_id?: number | null;
  client_name: string;
  owner_name: string;
  city: string;
  state: string;
  country: string;
  address?: string | null;
  phone_number: string;
  postalcode: string;
  created_at: string; // or Date
  updated_at: string; // or Date
  WorkReport: WorkReport[]; // Assuming you have a WorkReport interface defined
}

interface Carrier {
  carrier_id: number;
  name: string;
  register1?: string | null;
  code: string;
  country?: string | null;
  state?: string | null;
  city?: string | null;
  phone: string;
  postalcode?: string | null;
  address?: string | null;
  created_at: string; // or Date
  updated_at: string; // or Date
  corporation_id?: number | null;
  WorkReport: WorkReport[]; // Assuming you have a WorkReport interface defined
}

interface WorkType {
  id: number;
  work_type: string;
  created_at: string; // or Date
  updated_at: string;
  WorkReport: WorkReport[];
  category: any;
}

ModuleRegistry.registerModules([AllCommunityModule]);

const Column = (
  permissions: any[],
  setDeletedData: any,
  deleteData: boolean
) => {
  const [fromDate, setFromDate] = useState<string>("");
  const [toDate, setToDate] = useState<string>("");
  const columns = [

    {
      field: "date",
      headerName: "Date",
      valueGetter: (params) => {
        const date = params.data?.date;
        return date
          ? DateTime.fromISO(date, { zone: "utc" }).toFormat("dd-MM-yyyy")
          : "N/A";
      },
      filter: "agDateColumnFilter",
      filterParams: {
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          if (!cellValue) return -1;
          const cellDate = DateTime.fromISO(cellValue, { zone: "utc" })
            .startOf("day")
            .toJSDate();
          const filterDate = new Date(
            Date.UTC(
              filterLocalDateAtMidnight.getFullYear(),
              filterLocalDateAtMidnight.getMonth(),
              filterLocalDateAtMidnight.getDate()
            )
          );
          if (cellDate < filterDate) return -1;
          if (cellDate > filterDate) return 1;
          return 0;
        },
        buttons: ["apply", "reset"],
      },
      headerComponent: PinnedHeader,
    },
    {
      field: "user",
      headerName: "Username",
      valueGetter: (params) => params.data?.user?.username || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "client_name",
      headerName: "Client",
      valueGetter: (params) =>
        params.data?.client?.client_name || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "carriername",
      headerName: "Carrier",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
      valueGetter: (params) => params.data?.carrier?.name || "N/A",
    },
    {
      field: "work_type",
      headerName: "Work",
      valueGetter: (params) =>
        params.data?.work_type?.work_type || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "category",
      headerName: "Category",
      valueGetter: (params) =>
        params.data?.category?.category_name || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "task_type",
      headerName: "Type",
      valueGetter: (params) => params.data?.task_type || "N/A",
      filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["apply", "reset"],
    },

    headerComponent: PinnedHeader,
    },
    {
      field: "switch_type",
      headerName: "SwitchType",
      valueGetter: (params) => params.data?.switch_type || "null",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
    },
    {
      field: "actual_number",
      headerName: "Actual No",
      valueGetter: (params) => params.data?.actual_number || "",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
    },
    {
      field: "start_time",
      headerName: "Start Time",
      valueGetter: (params) => formatTimeZone(params.data?.start_time || ""),
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
      filter:false
    },
    {
      field: "finish_time",
      headerName: "Finish Time",
      valueGetter: (params) => formatTimeZone(params.data?.finish_time || ""),
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
      filter:false
    },
    // {
    //   field: "time_spent",
    //   headerName: "Time Spent",
    //   valueGetter: (params) => params.data?.time_spent || "N/A",
    // },
       {
          field: "time_spent",
          headerName: "Time Spent",
          valueGetter: ({ data }) => {
            const timeSpent = data?.time_spent;
            if (!timeSpent) return "-";
            const formatted = formatDuration(timeSpent);
            const [hours, minutes] = formatted.split(":");
            return `${hours}:${minutes}`;
          },
          cellStyle: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            textAlign: 'center', // Fallback
          },
          filter:false
        },
    {
      field: "notes",
      headerName: "Notes",
      valueGetter: (params) => params.data?.notes || "",
      filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["apply", "reset"],
    },

    headerComponent: PinnedHeader,
    },
    {
      field: "action",
      headerName: "Action",
      cellRenderer: (params) => {
        const workReport = params?.data;
        return (
          <div className="flex items-center">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["update-workReport"]}
            >
              <UpdateWorkReport workReport={params?.data} />
            </PermissionWrapper>
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["delete-workReport"]}
            >
              <DeleteRow
                  route={`${workreport_routes.DELETE_WORKREPORT}/${workReport?.id}`}
                  onSuccess={() => setDeletedData(!deleteData)}
              />
            </PermissionWrapper>
          </div>
        );
      },
      sortable: false,
      width: 130,
      minWidth: 100,
      pinned: "right",
      filter:false,
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
    },
  ];
  return columns;
};
export default Column;