"use client";
import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
  useContext,
} from "react";
import { FaExclamation<PERSON>riangle, Fa<PERSON><PERSON>er, FaSearch } from "react-icons/fa";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Pagination from "./Pagination";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { cn } from "@/lib/utils";
import { LuSearchX } from "react-icons/lu";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import CreateTicket from "../user/trackSheets/ticketing_system/CreateTicket";
import { EyeIcon, EyeOffIcon, RefreshCw } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  CheckCircle2,
  ExternalLink,
  Inbox,
  Ticket,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { BiFilterAlt } from "react-icons/bi";
import { TrackSheetContext } from "../user/trackSheets/TrackSheetContext";
import { Label } from "@/components/ui/label";
import { MdDensitySmall } from "react-icons/md";

// Custom cell renderer for selection checkbox
const SelectionCheckboxRenderer = (props: any) => {
  const isSelected = props.node.isSelected();
  const ticketId = props.data?.ticketId;
  const handleChange = (checked: boolean) => {
    props.node.setSelected(checked, false, true);
  };
  if (ticketId) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <a
                href={`/pms/manage_tickets/${ticketId}?from=tracksheets`}
                target="_blank"
                rel="noopener noreferrer"
                tabIndex={0}
                className="focus:outline-none"
              >
                <Badge
                  variant="outline"
                  className="flex items-center justify-center w-[22px] h-[22px] p-0.5 border-green-200 bg-green-50 text-green-700 hover:bg-green-100 transition cursor-pointer"
                  style={{ minWidth: 22, minHeight: 22 }}
                >
                  <ExternalLink className="w-4 h-4" />
                </Badge>
              </a>
            </TooltipTrigger>
            <TooltipContent side="right" className="text-xs max-w-[180px]">
              A ticket already exists for this row.
              <br />
              Click to view the ticket in a new tab.
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    );
  }
  return (
    <div className="flex items-center justify-center w-full h-full">
      <Checkbox
        checked={isSelected}
        onCheckedChange={handleChange}
        aria-label="Select row"
        className="h-4 w-4 border-2 border-primary rounded-sm bg-white"
      />
    </div>
  );
};

// Custom header component for "select all" functionality
const HeaderSelectionCheckboxRenderer = (props: any) => {
  const [isChecked, setIsChecked] = useState(false);
  const [isIndeterminate, setIsIndeterminate] = useState(false);

  const onSelectionChanged = useCallback(() => {
    const selectedNodes = props.api.getSelectedNodes();
    let displayedNodeCount = 0;
    props.api.forEachNodeAfterFilterAndSort(() => {
      displayedNodeCount++;
    });

    if (displayedNodeCount === 0) {
      setIsChecked(false);
      setIsIndeterminate(false);
      return;
    }

    const allSelected = selectedNodes.length === displayedNodeCount;
    const someSelected = selectedNodes.length > 0 && !allSelected;

    setIsChecked(allSelected);
    setIsIndeterminate(someSelected);
  }, [props.api]);

  useEffect(() => {
    props.api.addEventListener("selectionChanged", onSelectionChanged);
    props.api.addEventListener("modelUpdated", onSelectionChanged); // for filtering, sorting etc
    return () => {
      props.api.removeEventListener("selectionChanged", onSelectionChanged);
      props.api.removeEventListener("modelUpdated", onSelectionChanged);
    };
  }, [onSelectionChanged, props.api]);

  const handleChange = (checked: boolean) => {
    if (checked) {
      props.api.forEachNodeAfterFilterAndSort((node: any) => {
        if (!node.data?.ticketId) {
          node.setSelected(true);
        } else {
          node.setSelected(false);
        }
      });
    } else {
      props.api.forEachNodeAfterFilterAndSort((node: any) =>
        node.setSelected(false)
      );
    }
  };

  return (
    <div className="flex items-center justify-center w-full h-full">
      <Checkbox
        checked={isIndeterminate ? "indeterminate" : isChecked}
        onCheckedChange={handleChange}
        aria-label="Select all rows"
        className="h-4 w-4 border-2 border-primary rounded-sm bg-white"
      />
    </div>
  );
};

interface DataGridTableProps {
  columns: any[];
  data: any[];
  isLoading?: boolean;
  showColDropDowns?: boolean;
  filter?: boolean;
  filter1PlaceHolder?: string;
  filter2?: boolean;
  filter3?: boolean;
  filter3view?: JSX.Element;
  filter4?: boolean;
  filter4view?: JSX.Element;
  filter5?: boolean;
  filter5view?: JSX.Element;
  total?: boolean;
  totalview?: JSX.Element;
  filter_column?: string;
  filter_column2?: string;
  filter_column3?: string;
  filter_column4?: string;
  overflow?: boolean;
  totalPages?: number;
  showPageEntries?: boolean;
  className?: string;
  showSearchColumn?: boolean;
  pageSize?: number;
  isTimerRunning?: any;
  setIsTimerRunning?: any;
  onFilteredDataChange?: (filteredData: any[]) => void;
  customFieldsMap?: { [id: string]: { name: string; type: string } };
  selectedClients?: any[];
  showLegrandColumns?: boolean;
  onColumnVisibilityChange?: (visibility: { [key: string]: boolean }) => void;
}

const DataGridTableTrackSheet = ({
  columns,
  data,
  isLoading,
  showColDropDowns,
  filter,
  filter2,
  filter3 = false,
  filter3view,
  filter4,
  filter4view,
  filter5,
  filter5view,
  total,
  totalview,
  filter_column,
  filter_column2,
  filter_column3,
  filter_column4,
  totalPages,
  showPageEntries,
  className,
  filter1PlaceHolder,
  showSearchColumn = true,
  pageSize,
  customFieldsMap,
  selectedClients,
  showLegrandColumns,
  onColumnVisibilityChange,
}: DataGridTableProps) => {
  const [page, setPage] = useState<number>(pageSize || 50);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const searchParams = useSearchParams();

  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  const currentPage = Number(searchParams.get("page")) || 1;
  const currentPageSize =
    Number(searchParams.get("pageSize")) || pageSize || 50;

  const gridRef = useRef<AgGridReact>(null);

  const onFilterChanged = () => {
    const api = gridRef.current?.api;
    if (!api) return;
    const model = api.getFilterModel();
    const params = new URLSearchParams(searchParams);
    const oldFilterParams = new URLSearchParams();
    for (const [key, value] of searchParams.entries()) {
      const baseKey = key.replace(/_op$/, "");
      if (columns.some((col) => col.field === baseKey)) {
        oldFilterParams.set(key, value);
      }
    }
    const newFilterParams = new URLSearchParams();
    Object.entries(model).forEach(([field, filterComponent]) => {
      const { filterType, filter, conditions, operator } = filterComponent;
      if (filterType === "date" && Array.isArray(conditions)) {
        let from: string | null = null;
        let to: string | null = null;
        conditions.forEach((cond) => {
          if (["greaterThan", "greaterThanOrEqual"].includes(cond.type)) {
            from = cond.dateFrom;
          } else if (["lessThan", "lessThanOrEqual"].includes(cond.type)) {
            to = cond.dateFrom;
          } else if (cond.type === "equals") {
            if (!from || cond.dateFrom < from) from = cond.dateFrom;
            if (!to || cond.dateFrom > to) to = cond.dateFrom;
          }
        });
        if (from) newFilterParams.set(`${field}_from`, from);
        if (to) newFilterParams.set(`${field}_to`, to);
      } else if (filterType === "text" || filterComponent.type === "text") {
        if (filter) {
          newFilterParams.set(field, filter);
        } else if (Array.isArray(conditions)) {
          const values = conditions
            .map((c) => c.filter)
            .filter(Boolean)
            .join(",");
          if (values) newFilterParams.set(field, values);
        }
      } else if (Array.isArray(conditions)) {
        const values = conditions
          .map((c) => c.filter)
          .filter(Boolean)
          .join(",");
        if (values) {
          newFilterParams.set(field, values);
          if (operator) newFilterParams.set(`${field}_op`, operator);
        }
      } else if (filter) {
        newFilterParams.set(field, filter);
      }
    });
    if (Object.keys(model).length === 0) {
      columnsWithSerialNumber.forEach((col) => {
        params.delete(col.headerName);
        params.delete(col.field);
        params.delete(`${col.field}_from`);
        params.delete(`${col.field}_to`);
        if (
          typeof col.field === "string" &&
          col.field.startsWith("customField_")
        ) {
          params.delete(`${col.field}_from`);
          params.delete(`${col.field}_to`);
        }
      });
      [
        ["recievedFDate", "recievedTDate"],
        ["invoiceFDate", "invoiceTDate"],
        ["shipmentFDate", "shipmentTDate"],
      ].forEach(([from, to]) => {
        params.delete(from);
        params.delete(to);
      });
      for (const key of Array.from(params.keys())) {
        if (
          key.endsWith("_from") ||
          key.endsWith("_to") ||
          key.endsWith("_op")
        ) {
          params.delete(key);
        }
      }
      params.delete("page");
      replace(`${pathname}?${params.toString()}`);
      return;
    }
    if (oldFilterParams.toString() !== newFilterParams.toString()) {
      for (const key of oldFilterParams.keys()) {
        params.delete(key);
      }
      for (const [key, value] of newFilterParams.entries()) {
        params.set(key, value);
      }
      replace(`${pathname}?${params.toString()}`);
    }
  };

  const onSortChanged = () => {
    const api = gridRef.current?.api;
    if (!api) return;

    // Get sorted columns and sort them by sortIndex to maintain order
    const sortModel = api.getColumnState()
      .filter((col) => col.sort)
      .sort((a, b) => (a.sortIndex || 0) - (b.sortIndex || 0));

    const params = new URLSearchParams(searchParams);
    params.delete("sortBy");
    params.delete("order");

    if (sortModel && sortModel.length > 0) {
      const sortBy = sortModel.map((s) => s.colId).filter(Boolean).join(",");
      const order = sortModel.map((s) => s.sort).filter(Boolean).join(",");

      if (sortBy && order) {
        params.set("sortBy", sortBy);
        params.set("order", order);
      }
    }

    // Reset to page 1 when sorting changes
    // params.set("page", "1");
    replace(`${pathname}?${params.toString()}`);
  };

  const onGridReady = (params: any) => {
    const api = params.api;
    const sortBy = searchParams.get("sortBy");
    const order = searchParams.get("order");

    if (sortBy && order) {
      const sortByArr = sortBy.split(",").map(s => s.trim()).filter(Boolean);
      const orderArr = order.split(",").map(s => s.trim()).filter(Boolean);

      const newState = api.getColumnState().map((col: any) => {
        const idx = sortByArr.indexOf(col.colId);
        let sort: "asc" | "desc" | undefined = undefined;

        if (idx !== -1) {
          const ord = orderArr[idx]?.toLowerCase();
          if (ord === "asc" || ord === "desc") {
            sort = ord;
          }
        }

        return { ...col, sort, sortIndex: idx !== -1 ? idx : null };
      });

      api.applyColumnState({
        state: newState,
        applyOrder: true,
        defaultState: { sort: null, sortIndex: null }
      });
    }
  };

  const [columnVisibility, setColumnVisibility] = useState<{
    [key: string]: boolean;
  }>({});
  const [isInitialized, setIsInitialized] = useState(false);

  const onSelectionChanged = () => {
    if (gridRef.current && gridRef.current.api) {
      setSelectedRows(gridRef.current.api.getSelectedRows());
    }
  };

  const handleClearSelection = useCallback(() => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.deselectAll();
    }
    setSelectedRows([]);
    setIsTicketModalOpen(false);
  }, []);

  const processedData = useMemo(() => {
    return data?.map((item, index) => ({
      ...item,
      stableId: (currentPage - 1) * currentPageSize + index + 1,
    }));
  }, [data, currentPage, currentPageSize]);

  const columnsWithSerialNumber = useMemo(() => {
    return [
      {
        width: 80,
        pinned: "left",
        lockVisible: true,
        suppressHeaderMenuButton: true,
        headerName: "",
        headerComponent: HeaderSelectionCheckboxRenderer,
        cellRenderer: SelectionCheckboxRenderer,
        suppressMovable: true,
        suppressSizeToFit: true,
        resizable: false,
        suppressNavigable: true,
        suppressMenu: true,
        suppressColumnsToolPanel: true,
        suppressAutoSize: true,
        suppressCellSelection: true,
      },
      {
        headerName: "Sr. No.",
        field: "stableId",
        sortable: true,
        width: 80,
        minWidth: 80,
        maxWidth: 80,
        cellStyle: {
          textAlign: "center",
        },
        pinned: "left",
        filter: false,
        comparator: (valueA: number, valueB: number) => {
          // Ensure proper numeric comparison
          const numA = Number(valueA) || 0;
          const numB = Number(valueB) || 0;
          return numA - numB;
        },
        sortingOrder: ["asc", "desc"],
      },
      ...columns.filter((col) => col.field !== "sr_no"),
    ];
  }, [columns]);

  useEffect(() => {
    // Initialize visibility state for all columns only once
    if (!isInitialized) {
      const initialVisibility: { [key: string]: boolean } = {};
      columnsWithSerialNumber.forEach((col) => {
        initialVisibility[col.field] = true;
      });
      setColumnVisibility(initialVisibility);
      onColumnVisibilityChange?.(initialVisibility);
      setIsInitialized(true);
    }
  }, [columnsWithSerialNumber, isInitialized, onColumnVisibilityChange]);

  useMemo(() => {
    if (gridRef.current && gridRef.current.api) {
      if (isLoading) {
        gridRef.current.api.showLoadingOverlay();
      } else if (!processedData || processedData.length === 0) {
        gridRef.current.api.showNoRowsOverlay();
      } else {
        gridRef.current.api.hideOverlay();
      }
    }
  }, [isLoading, processedData]);

  const handlePageChange = (e: any) => {
    const newPageSize = parseInt(e.target.value);
    setPage(newPageSize);
    if (totalPages) {
      params.set("pageSize", newPageSize?.toString());
      replace(`${pathname}?${params.toString()}`);
    }
  };

  const toggleColumnVisibility = (field: string, isVisible: boolean) => {
    setColumnVisibility((prevVisibility) => {
      const newVisibility = {
        ...prevVisibility,
        [field]: isVisible,
      };
      onColumnVisibilityChange?.(newVisibility);
      return newVisibility;
    });

    // Update AG Grid directly without using effect
    if (gridRef.current?.api) {
      gridRef.current.api.setColumnsVisible([field], isVisible);
    }
  };
  const { warningFilter, setWarningFilter, hasTickets, setHasTickets } =
    useContext(TrackSheetContext);

  return (
    <div className={`animate-in fade-in duration-1000`}>
      {selectedRows.length > 0 && (
        <div className="fixed bottom-4 left-1/2 -translate-x-1/2 z-20 bg-background border border-primary shadow-lg rounded-lg px-4 py-2 flex items-center gap-4 animate-in fade-in slide-in-from-bottom-2 duration-300">
          <span className="text-sm font-medium text-primary">
            {selectedRows.length} item(s) selected
          </span>
          <Button
            variant="default"
            size="sm"
            onClick={() => setIsTicketModalOpen(true)}
          >
            Create Ticket
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={handleClearSelection}
          >
            <LuSearchX className="h-4 w-4" />
            <span className="sr-only">Clear Selection</span>
          </Button>
        </div>
      )}

      {isTicketModalOpen && (
        <CreateTicket
          isOpen={isTicketModalOpen}
          onClose={() => setIsTicketModalOpen(false)}
          onClearSelection={handleClearSelection}
          selectedRows={selectedRows}
        />
      )}
      <div className="flex w-full lg:w-full lg:flex mb-3 animate-in fade-in duration-1000 gap-5">
        {selectedClients.length > 0 && (
          <div className="flex items-center gap-3 ml-2 border-l pl-2 border-r pr-2 border-gray-200 dark:border-neutral-700">
            <button
              onClick={() =>
                setHasTickets(hasTickets === "true" ? null : "true")
              }
              className={`text-xs font-medium flex items-center gap-1 pb-1 transition-all
        ${
          hasTickets === "true"
            ? "text-green-600 border-b-2 border-green-500 dark:text-green-400"
            : "text-gray-500 hover:text-gray-700 dark:text-gray-400"
        }`}
            >
              <Ticket className="w-3.5 h-3.5" />
              Tickets
            </button>

            <button
              onClick={() =>
                setHasTickets(hasTickets === "false" ? null : "false")
              }
              className={`text-xs font-medium flex items-center gap-1.5 pb-1 transition-all
        ${
          hasTickets === "false"
            ? "text-yellow-600 border-b-2 border-yellow-500 dark:text-yellow-400"
            : "text-gray-500 hover:text-gray-700 dark:text-gray-400"
        }`}
            >
              <Inbox className="w-3.5 h-3.5" />
              Empty
            </button>
          </div>
        )}
          <div className="flex-1" />

        <div className="flex items-center gap-3 ml-2 border-l pl-2 border-r pr-2 border-gray-200 dark:border-neutral-700">
          <button
            onClick={() =>
              setWarningFilter(warningFilter === "true" ? null : "true")
            }
            className={`text-xs font-medium flex items-center gap-1 pb-1 transition-all
      ${
        warningFilter === "true"
          ? "text-red-600 border-b-2 border-red-500 dark:text-red-400"
          : "text-gray-500 hover:text-gray-700 dark:text-gray-400"
      }`}
          >
            <AlertCircle className="w-3.5 h-3.5" />
            Warnings
          </button>

          <button
            onClick={() =>
              setWarningFilter(warningFilter === "false" ? null : "false")
            }
            className={`text-xs font-medium flex items-center gap-1.5 pb-1 transition-all
      ${
        warningFilter === "false"
          ? "text-green-600 border-b-2 border-green-500 dark:text-green-400"
          : "text-gray-500 hover:text-gray-700 dark:text-gray-400"
      }`}
          >
            <CheckCircle2 className="w-3.5 h-3.5" />
            Empty
          </button>
        </div>

        {showPageEntries && (
          <div className="relative">
            <select
              value={page}
              onChange={handlePageChange}
              className="
        pl-3 pr-4 rounded-lg border border-gray-300 dark:border-neutral-700
        bg-white dark:bg-neutral-900 text-sm text-gray-700 dark:text-gray-200
        appearance-none cursor-pointer h-9 shadow-sm transition duration-150
        focus:outline-none
      "
              aria-label="Items per"
            >
              <option value={10}>10</option>
              <option value={15}>15</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
              <option value={250}>250</option>
              <option value={500}>500</option>
              <option value={1000}>1000</option>
              <option value={1500}>1500</option>
              <option value={2000}>2000</option>
            </select>

            {/* Custom dropdown arrow */}
            <div
              className="
     absolute right-1 top-1/2 -translate-y-1/2 pointer-events-none text-gray-400 dark:text-gray-500
    "
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M6 9l6 6 6-6" />
              </svg>
            </div>
          </div>
        )}

        {showColDropDowns && (
          <div className="relative">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button
                  className="
            flex items-center gap-2 px-2.5 py-1.5 rounded-lg
            border border-gray-300 dark:border-neutral-700
            bg-white dark:bg-neutral-900
            text-sm text-gray-700 dark:text-gray-200
            shadow-sm hover:bg-gray-50 dark:hover:bg-neutral-800
            transition-colors focus:outline-none
          "
                  aria-label="Column filters"
                >
                  <div className="flex items-center gap-2">
                    <BiFilterAlt className="text-gray-600 dark:text-gray-300 text-lg" />
                    <span className="hidden sm:inline">
                      Columns
                    </span>
                  </div>
                </button>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                className="
          bg-white
          rounded-lg shadow-lg
          border border-gray-200 dark:border-gray-700
          p-2 min-w-[200px]
          animate-in fade-in-80 zoom-in-95
        "
                align="end"
              >
                <div className="px-2 py-1.5">
                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Toggle Columns
                  </p>
                </div>

                <div className="max-h-64 overflow-y-auto thin-scrollbar">
                  {columnsWithSerialNumber
                    .filter(
                      (column) =>
                        column.hideable !== false &&
                        column.field &&
                        column.headerName !== ""
                    )
                    .map((column) => (
                      <DropdownMenuCheckboxItem
                        key={column.field}
                        className="
                  px-2 py-1.5 rounded-md
                  text-sm text-gray-700 
                  hover:bg-gray-100 
                  focus:bg-gray-100 
                  cursor-pointer
                  transition-colors
                  flex items-center
                "
                        checked={columnVisibility[column.field] ?? true}
                        onCheckedChange={(value) => {
                          toggleColumnVisibility(column.field, value);
                        }}
                        onSelect={(e) => e.preventDefault()}
                      >
                        <div className="flex items-center">
                          <span className="mr-2">
                            {columnVisibility[column.field] ?? true ? (
                              <EyeIcon className="h-4 w-4 text-black-500" />
                            ) : (
                              <EyeOffIcon className="h-4 w-4 text-gray-400" />
                            )}
                          </span>
                          <span className="truncate">
                            {column.headerName || column.field}
                          </span>
                        </div>
                      </DropdownMenuCheckboxItem>
                    ))}
                </div>
                <div className="border-t border-gray-200 mt-1 pt-1 px-2">
                  <button
                    className="
              text-xs text-black-600
              hover:underline
              text-left py-1 flex gap-1
            "
                    onClick={() => {
                      const newVisibility = {};
                      const fieldsToShow = [];
                      columnsWithSerialNumber.forEach((col) => {
                        if (
                          col.hideable !== false &&
                          col.field &&
                          col.headerName !== ""
                        ) {
                          newVisibility[col.field] = true;
                          fieldsToShow.push(col.field);
                        } else if (col.field) {
                          newVisibility[col.field] = false;
                        }
                      });
                      if (gridRef.current?.api) {
                        gridRef.current.api.setColumnsVisible(
                          fieldsToShow,
                          true
                        );
                        // Hide columns that are not in fieldsToShow and have a field
                        columnsWithSerialNumber.forEach((col) => {
                          if (col.field && !fieldsToShow.includes(col.field)) {
                            gridRef.current.api.setColumnsVisible(
                              [col.field],
                              false
                            );
                          }
                        });
                      }
                      setColumnVisibility(newVisibility);
                      onColumnVisibilityChange?.(newVisibility);
                    }}
                  >
                    <RefreshCw className="h-3 w-3 " />
                    Reset to default
                  </button>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {total && <div>{totalview}</div>}

      <div className={cn("", className)}>
        <div
          className="ag-theme-alpine custom-ag-grid"
          style={{ height: "100vh", width: "100%" }}
        >
          <AgGridReact
            ref={gridRef}
            rowData={processedData}
            columnDefs={columnsWithSerialNumber}
            headerHeight={35}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
            enableCellTextSelection={true}
            alwaysMultiSort={true}
            multiSortKey="ctrl"
            suppressMenuHide={false}
            onGridReady={onGridReady}
            // domLayout="autoHeight"
            // overlayNoRowsTemplate={noRowsOverlayTemplate}
            // onGridReady={(params) => {
            //   params.api.sizeColumnsToFit();
            //   // Show overlays on grid ready
            //   if (isLoading) {
            //     params.api.showLoadingOverlay();
            //   } else if (!processedData || processedData.length === 0) {
            //     params.api.showNoRowsOverlay();
            //   } else {
            //     params.api.hideOverlay();
            //   }
            // }}
            // // onFirstDataRendered={(params) => {
            // //   params.api.sizeColumnsToFit();
            // // }}
            // onColumnVisible={(event) => {
            //   event.api.sizeColumnsToFit();
            // }}
            // onGridSizeChanged={(params) => {
            //   params.api.sizeColumnsToFit();
            // }}
            rowSelection="multiple"
            suppressRowClickSelection={true}
            onSelectionChanged={onSelectionChanged}
            defaultColDef={{
              sortable: true,
              resizable: true,
              cellStyle: { borderRight: "1px solid #ddd" },
              filter: true,
              floatingFilter: false,
            }}
          />
        </div>
      </div>

      {data && (
        <Pagination
          currentPage={
            totalPages
              ? Number(params.get("page")) || 1
              : (gridRef.current?.api?.paginationGetCurrentPage() || 0) + 1
          }
          totalPages={
            totalPages
              ? totalPages
              : gridRef.current?.api?.paginationGetTotalPages() || 1
          }
          onPageChange={(page: number) => {
            if (gridRef.current) {
              gridRef?.current?.api?.paginationGoToPage(page - 1);
            }

            if (totalPages) {
              params.set("page", page.toString());
              replace(`${pathname}?${params.toString()}`);
            }
          }}
        />
      )}
    </div>
  );
};

export default DataGridTableTrackSheet;
