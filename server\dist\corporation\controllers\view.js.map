{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../../../src/corporation/controllers/view.ts"], "names": [], "mappings": ";;;AAAA,iDAAkD;AAClD,uDAA0D;AAEnD,MAAM,eAAe,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAChD,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QACjD,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAVW,QAAA,eAAe,mBAU1B;AAEK,MAAM,QAAQ,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACzC,IAAI,CAAC;QACH,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,OAAO,EACP,KAAK,EACL,MAAM,GAAG,IAAI,EACb,KAAK,GAAG,MAAM,GACf,GAAG,GAAG,CAAC,KAAK,CAAC;QAEd,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEnD,MAAM,gBAAgB,GAAU,EAAE,CAAC;QAEnC,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAChE,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBAC1B,QAAQ,EAAE;wBACR,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,aAAa;qBACpB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACtE,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;oBACpC,SAAS,EAAE;wBACT,QAAQ,EAAE,SAAS;wBACnB,IAAI,EAAE,aAAa;qBACpB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACpE,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;oBAClC,QAAQ,EAAE;wBACR,QAAQ,EAAE,QAAQ;wBAClB,IAAI,EAAE,aAAa;qBACpB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9D,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBAC5B,KAAK,EAAE;wBACL,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE,aAAa;qBACpB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC5D,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBAC1B,IAAI,EAAE;wBACJ,IAAI,EAAE;4BACJ,QAAQ,EAAE,IAAI;4BACd,IAAI,EAAE,aAAa;yBACpB;qBACF;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAClD,IAAI,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAC1B,CAAC;YAEF,sEAAsE;YACtE,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC7C,KAAK,EAAE;oBACL,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;wBAChC,QAAQ,EAAE;4BACR,QAAQ,EAAE,OAAO;4BACjB,IAAI,EAAE,aAAa;yBACpB;qBACF,CAAC,CAAC;iBACJ;aACF,CAAC,CAAC;YAEH,+BAA+B;YAC/B,oDAAoD;YACpD,6EAA6E;YAC7E,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE;oBACF;wBACE,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;4BAChC,QAAQ,EAAE;gCACR,QAAQ,EAAE,OAAO;gCACjB,IAAI,EAAE,aAAa;6BACpB;yBACF,CAAC,CAAC;qBACJ;oBACD;wBACE,SAAS,EAAE;4BACT,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;yBACjC;qBACF;iBACF;aACF,CAAC,CAAC;QACL,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAE9D,6CAA6C;YAC7C,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACrD,KAAK,EAAE;oBACL,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;wBAC5B,KAAK,EAAE;4BACL,QAAQ,EAAE,KAAK;4BACf,IAAI,EAAE,aAAa;yBACpB;qBACF,CAAC,CAAC;iBACJ;gBACD,MAAM,EAAE;oBACN,KAAK,EAAE,IAAI;iBACZ;aACF,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,cAAc;iBAC1B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;iBACnB,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;YAE7B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,gBAAgB,CAAC,IAAI,CAAC;oBACpB,KAAK,EAAE;wBACL,EAAE,EAAE,MAAM;qBACX;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAoB,EAAE,CAAC;QAExC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,WAAW,CAAC,GAAG,GAAG,gBAAgB,CAAC;QACrC,CAAC;QAED,wFAAwF;QACxF,MAAM,OAAO,GAAG,MAAM,IAAA,4BAAe,EAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAG3E,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtC,KAAK,EAAE,WAAW;YAClB,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC7B,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC7B,OAAO,EAAE;gBACP,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE;oBACX,OAAO,EAAE;wBACP,MAAM,EAAE,IAAI;qBACb;iBACF;aACF;YACD,OAAO;SACR,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACzC,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAxLW,QAAA,QAAQ,YAwLnB;AAEK,MAAM,WAAW,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC5C,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACzC,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;QACH,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAZW,QAAA,WAAW,eAYtB;AAEK,MAAM,qBAAqB,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACtD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC;QAC3B,MAAM,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC;QAEhD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,4DAA4D;YAC5D,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YACzD,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,EAAE,EAAE,WAAW,CAAC,cAAc;oBAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAC;YACL,CAAC;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,6CAA6C;aACvD,CAAC,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GACV,cAAc,KAAK,MAAM;YACvB,CAAC,CAAC;gBACE,EAAE,EAAE,IAAI;gBACR,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,IAAI,EAAE,IAAI;wBACV,eAAe,EAAE;4BACf,MAAM,EAAE;gCACN,UAAU,EAAE;oCACV,MAAM,EAAE;wCACN,MAAM,EAAE,IAAI;wCACZ,MAAM,EAAE,IAAI;qCACb;iCACF;6BACF;yBACF;qBACF;iBACF;aACF;YACH,CAAC,CAAC;gBACE,EAAE,EAAE,IAAI;gBACR,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,IAAI;gBACX,WAAW,EAAE;oBACX,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;iBAC1B;gBACD,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,IAAI,EAAE,IAAI;wBACV,eAAe,EAAE;4BACf,MAAM,EAAE;gCACN,UAAU,EAAE;oCACV,MAAM,EAAE;wCACN,MAAM,EAAE,IAAI;wCACZ,MAAM,EAAE,IAAI;qCACb;iCACF;6BACF;yBACF;qBACF;iBACF;gBACD,UAAU,EAAE,IAAI;aACjB,CAAC;QAER,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM;aACX;YACD,MAAM;SACP,CAAC,CAAC;QACH,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QACD,gBAAgB;QAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAxFW,QAAA,qBAAqB,yBAwFhC"}