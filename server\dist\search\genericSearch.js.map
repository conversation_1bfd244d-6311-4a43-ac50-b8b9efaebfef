{"version": 3, "file": "genericSearch.js", "sourceRoot": "", "sources": ["../../src/search/genericSearch.ts"], "names": [], "mappings": ";;;;;;AAAA,iDAA4C;AAC5C,2BAA+C;AAC/C,gDAAwB;AA2BxB,0DAA0D;AAC1D,IAAI,WAAW,GAIJ,IAAI,CAAC;AAEhB,MAAM,gBAAgB,GAAG,CAAC,SAAiB,EAAU,EAAE;IACrD,MAAM,KAAK,GAAG,IAAA,gBAAW,EAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CACnD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CACzB,CAAC;IACF,OAAO,KAAK;SACT,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAA,iBAAY,EAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;SAChE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,8BAA8B;AAC9B,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE;IACjC,IAAI,WAAW;QAAE,OAAO,WAAW,CAAC;IACpC,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC/D,8EAA8E;QAC9E,MAAM,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC3C,eAAe;QAEf,MAAM,IAAI,GAAG,MAAM,IAAA,mBAAO,EAAC,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;QAElD,MAAM,eAAe,GAA2C,EAAE,CAAC;QACnE,MAAM,cAAc,GAA6B,EAAE,CAAC;QAEpD,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3C,eAAe,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YAChC,cAAc,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YAE/B,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC7B,eAAe,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;gBAEpD,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC5B,cAAc,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,UAAU,GAA6B,EAAE,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACxC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,WAAW,GAAG,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,CAAC;QAC9D,OAAO,WAAW,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAEF,kDAAkD;AAC3C,MAAM,qBAAqB,GAAG,KAAK,IAAI,EAAE;IAC9C,MAAM,eAAe,EAAE,CAAC;AAC1B,CAAC,CAAC;AAFW,QAAA,qBAAqB,yBAEhC;AAEK,MAAM,aAAa,GAAG,KAAK,EAAE,EAClC,KAAK,EACL,OAAO,GAAG,EAAE,EACZ,cAAc,GAAG,EAAE,EACnB,SAAS,EACT,eAAe,GAAG,EAAE,EACpB,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,MAAM,GAAG,IAAI,EACb,KAAK,GAAG,MAAM,EACd,OAAO,GACa,EAAE,EAAE;IACxB,qCAAqC;IACrC,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,MAAM,eAAe,EAAE,CAAC;IAC1B,CAAC;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACzB,MAAM,IAAI,GAAG,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;IACrD,MAAM,IAAI,GAAG,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;IAClE,MAAM,gBAAgB,GAAU,EAAE,CAAC;IACnC,MAAM,eAAe,GAAI,KAAgB,CAAC,WAAW,EAAE,CAAC;IAExD,yBAAyB;IACzB,KAAK,MAAM,QAAQ,IAAI,cAAc,EAAE,CAAC;QACtC,MAAM,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;QACxC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE;gBAAE,SAAS;YACpE,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9D,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;oBAC7B,CAAC,QAAQ,CAAC,EAAE;wBACV,CAAC,KAAK,CAAC,EAAE;4BACP,QAAQ,EAAE,MAAM;4BAChB,IAAI,EAAE,aAAa;yBACpB;qBACF;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,4BAA4B;IAC5B,IAAI,SAAS,EAAE,IAAI,IAAI,SAAS,EAAE,EAAE,EAAE,CAAC;QACrC,MAAM,aAAa,GAAQ,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;QACrD,IAAI,SAAS,CAAC,IAAI;YAAE,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC;QACxE,IAAI,SAAS,CAAC,EAAE;YAAE,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE,CAAC;QACpE,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC;IAED,6BAA6B;IAC7B,MAAM,YAAY,GAAG,WAAW,EAAE,eAAe,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;IACzE,MAAM,UAAU,GAAG,WAAW,EAAE,UAAU,IAAI,EAAE,CAAC;IAEjD,+CAA+C;IAC/C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;QAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE;YAAE,SAAS;QAEpE,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;QAEtC,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9D,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;oBAC7B,CAAC,KAAK,CAAC,EAAE;wBACP,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,aAAa;qBACpB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3D,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;aAAM,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,0BAA0B;YAC1B,MAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,kBAAkB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAC7D,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAC9D,CAAC;YAEF,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,gBAAgB,CAAC,IAAI,CAAC;oBACpB,CAAC,KAAK,CAAC,EAAE;wBACP,EAAE,EAAE,kBAAkB;qBACvB;iBACF,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,yBAAyB;IACzB,MAAM,WAAW,GAAG;QAClB,GAAG,EAAE,CAAC,GAAG,gBAAgB,EAAE,GAAG,eAAe,CAAC;QAC9C,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KAC3D,CAAC;IAEF,mEAAmE;IACnE,IAAI,OAAO,GAA4B,EAAE,CAAC;IAE1C,IAAI,gBAAgB,EAAE,CAAC;QACrB,MAAM,cAAc,GAAG,WAAW,EAAE,cAAc,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAE1E,IAAI,kBAA4B,CAAC;QAEjC,IAAI,gBAAgB,KAAK,IAAI,EAAE,CAAC;YAC9B,wBAAwB;YACxB,kBAAkB,GAAG,cAAc,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,0CAA0C;YAC1C,kBAAkB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CACnD,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC7B,CAAC;QACJ,CAAC;QAED,kBAAkB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACtC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kFAAkF;IAClF,IAAI,YAAiB,CAAC;IAEtB,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QACtC,gDAAgD;QAChD,YAAY,GAAG,OAAO,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,wDAAwD;QACxD,MAAM,cAAc,GAAG,WAAW,EAAE,cAAc,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAC1E,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,eAAe,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QACrF,YAAY,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC;QAEnC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACvD,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACxD,MAAM,WAAW,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzF,oEAAoE;YACpE,YAAY,GAAG,UAAU;iBACtB,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBAClB,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBACnE,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC;gBAChD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;iBACD,MAAM,CAAC,OAAO,CAAC,CAAC;YACnB,qCAAqC;YACrC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,YAAY,GAAG,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;aAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5E,yEAAyE;YACzE,YAAY,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAED,sBAAsB;IACtB,IAAI,YAAY,GAAuB;QACrC,KAAK,EAAE,WAAW;QAClB,IAAI;QACJ,IAAI;QACJ,OAAO,EAAE,YAAY;KACtB,CAAC;IAEF,6CAA6C;IAC7C,IAAI,MAAM,EAAE,CAAC;QACX,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;IAC/B,CAAC;SAAM,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;QAC/D,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC;IACjC,CAAC;IAED,gBAAgB;IAChB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC9B,MAAM,IAAI,GAAG,MAAO,MAAM,CAAC,KAAK,CAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACjE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC;IAE1C,MAAM,UAAU,GAAG,MAAO,MAAM,CAAC,KAAK,CAAS,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;IAE9E,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;AAC9B,CAAC,CAAC;AA3LW,QAAA,aAAa,iBA2LxB"}