"use client";
import React, { useContext, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import DeleteRow from "@/app/_component/DeleteRow";
import {
  formatDate,
  formatDuration,
  formatTimeZone,
  storeData,
  subtractTime,
} from "@/lib/swrFetching";
import { FaHistory, FaPlay } from "react-icons/fa";
import { formSubmit } from "@/lib/helpers";
import { workreport_routes } from "@/lib/routePath";
import { useRouter, useSearchParams } from "next/navigation";
import { useParentContext } from "./ParentContext";
import { revalidatePath } from "next/cache";
import { Badge } from "@/components/ui/badge";
import { TrackerContext } from "./TrackerContext";
import { toast } from "sonner";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import TriggerButton from "@/app/_component/TriggerButton";
import PauseResumeHistory from "./PauseResumeHistory/PauseResumeHistory";
import UpdateTracker from "./UpdateTracker";
import { permission } from "process";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { DateTime } from "luxon";
import { ColDef } from "ag-grid-community";
import PinnedHeader from "@/app/_component/PinnedHeader";

export interface WorkReport {
  notes?: string;
  date: Date;
  start_time: Date;
  finish_time?: Date;
  time_spent?: number;
  client: any;
  carrier?: any;
  work_type: any;
  category: any;
  planning_nummbers?: string;
  id: any;
  work_status: any;
  handleResumeTask: any;
  carrier_id: any;
  task_type: any;
  pause: any;
  user: any;
}

const Column = (
  isTimerRunning: boolean,
  setElapsedTime: React.Dispatch<React.SetStateAction<number>>,
  setIsTimerRunning: React.Dispatch<React.SetStateAction<boolean>>,
  setPreviousSelectedClient: React.Dispatch<React.SetStateAction<any>>,
  setPreviousSelectedCarrier: React.Dispatch<React.SetStateAction<any>>,
  permissions: any[]
) => {
  const columns = [
    {
      field: "date",
      headerName: "Date",
      valueGetter: (params) => {
        const date = params.data?.date;
        return date
          ? DateTime.fromISO(date, { zone: "utc" }).toFormat("dd-MM-yyyy")
          : "N/A";
      },
      filter: "agDateColumnFilter",
      filterParams: {
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          if (!cellValue) return -1;
          const cellDate = DateTime.fromISO(cellValue, { zone: "utc" })
            .startOf("day")
            .toJSDate();
          const filterDate = new Date(
            Date.UTC(
              filterLocalDateAtMidnight.getFullYear(),
              filterLocalDateAtMidnight.getMonth(),
              filterLocalDateAtMidnight.getDate()
            )
          );
          if (cellDate < filterDate) return -1;
          if (cellDate > filterDate) return 1;
          return 0;
        },
        buttons: ["apply", "reset"],
      },
      headerComponent: PinnedHeader,
    },
    {
      field: "username",
      headerName: "Username",
      valueGetter: ({ data }) => data?.user?.username || "-",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "clientname",
      headerName: "Client",
      valueGetter: ({ data }) => data?.client?.client_name || "-",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "carriername",
      headerName: "Carrier",
      valueGetter: ({ data }) =>
        data?.carrier_id === null ? "N/A" : data?.carrier?.name || "-",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "work_type",
      headerName: "Work Type",
      valueGetter: ({ data }) => data?.work_type?.work_type || "-",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "category",
      headerName: "Category",
      valueGetter: ({ data }) => data?.category?.category_name || "-",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "task_type",
      headerName: "Type",
      valueGetter: ({ data }) => data?.task_type || "-",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "start_time",
      headerName: "Start Time",
      // valueFormatter: ({ value }) => {
      //   if (!value) return "-";
      //   const date = new Date(value);
      //   return date.toLocaleTimeString("en-US", {
      //     hour: "2-digit",
      //     minute: "2-digit",
      //     hour12: true,
      //   });
      // },
      valueGetter: (params) => formatTimeZone(params.data?.start_time || ""),
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
      filter:false
    },
    {
      field: "finish_time",
      headerName: "Finish Time",
      // valueFormatter: ({ value }) => {
      //   if (!value) return "-";
      //   const date = new Date(value);
      //   return date.toLocaleTimeString("en-US", {
      //     hour: "2-digit",
      //     minute: "2-digit",
      //     hour12: true,
      //   });
      // },
      valueGetter: (params) => formatTimeZone(params.data?.finish_time || ""),
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
      filter:false
    },
    // {
    //   accessorKey: "pause",
    //   header: "History",
    //   cell: ({ row }) => {
    //     return (
    //       <>
    //         <PauseResumeHistory data={row.original} />
    //       </>
    //     );
    //   },
    // },
    // {
    //   accessorKey: "work_status",
    //   header: "Workstatus",
    //   cell: ({ row }) => {
    //     const handleResumeTask = async (
    //       workReportId: number,

    //       isTimerRunning: boolean
    //     ) => {
    //       if (!isTimerRunning) {
    //         localStorage.removeItem("timerData");

    //         const response = await formSubmit(
    //           `${workreport_routes.UPDATE_WORKREPORT}/${workReportId}`,
    //           "PUT",

    //           {
    //             action: "resume",
    //             work_status: "RESUMED",
    //           },
    //           "/user/tracker"
    //         );

    //         if (response.success) {
    //           const time =
    //             row.original.time_spent &&
    //             subtractTime(row.original.time_spent);

    //           setIsTimerRunning(true);
    //           setElapsedTime((prev) => {
    //             const updatedTime = prev + 1;
    //             storeData("timerData", {
    //               startTime: time,
    //               elapsedTime: updatedTime,
    //             });

    //             return updatedTime;
    //           });

    //           setPreviousSelectedClient(row.original.client);
    //           setPreviousSelectedCarrier(row.original.carrier);

    //           localStorage.setItem(
    //             "workType",
    //             JSON.stringify(parseInt(row.original.work_type.id))
    //           );
    //           localStorage.setItem(
    //             "client",
    //             JSON.stringify(row.original.client)
    //           );
    //           localStorage.setItem(
    //             "carrier",
    //             JSON.stringify(row.original.carrier)
    //           );

    //           router.refresh();
    //         }
    //       } else {
    //         toast.error("Timer is running. Pause or stop it first.");
    //       }
    //     };
    //     const work_status = row.original.work_status;
    //     if (work_status === "PAUSED") {
    //       return (
    //         <Badge
    //           // onClick={()=>{ ('onclick')}}
    //           onClick={() => handleResumeTask(row.original.id, isTimerRunning)}
    //           className=" cursor-pointer text-center w-20  flex items-center text-white bg-orange-500 hover:bg-orange-600 "
    //         >
    //           {}
    //           PAUSED
    //           {/* <FaPlay className="text-sm " /> */}
    //         </Badge>
    //       );
    //     }
    //     return (
    //       <Badge
    //         className={`cursor-pointer flex items-center gap-2 text-center w-20  justify-center text-white ${
    //           work_status === "FINISHED"
    //             ? "bg-gray-500"
    //             : work_status === "RESUMED"
    //             ? "bg-blue-500"
    //             : "bg-green-500"
    //         } cursor-pointer`}
    //       >
    //         {work_status}
    //       </Badge>
    //     );
    //   },
    // },

    {
      field: "time_spent",
      headerName: "Time Spent",
      valueGetter: ({ data }) => {
        const timeSpent = data?.time_spent;
        if (!timeSpent) return "-";
        const formatted = formatDuration(timeSpent);
        const [hours, minutes] = formatted.split(":");
        return `${hours}:${minutes}`;
      },
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
      filter:false
    },
    {
      field: "actual_number",
      headerName: "Actual No",
      filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["apply", "reset"],
    },

    headerComponent: PinnedHeader,
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
    },
    {
      field: "notes",
      headerName: "Notes",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },
  
      headerComponent: PinnedHeader,
    },
    {
      field: "action",
      headerName: "Action",
      cellRenderer: (params: any) => {
        const workReport = params?.data;
        return (
          <div className="flex items-center gap-2">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["update-tracker"]}
            >
              <UpdateTracker workReport={workReport} />
            </PermissionWrapper>
          </div>
        );
      },
      sortable: false,
      filter:false,
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
    },
  ];
  return columns;
};

export default Column;
